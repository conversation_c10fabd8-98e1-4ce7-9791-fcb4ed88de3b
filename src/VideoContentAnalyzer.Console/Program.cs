using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Serilog;
using Spectre.Console;
using System.CommandLine;
using System.CommandLine.Invocation;
using System.Text.Json;
using VideoContentAnalyzer.Console;
using VideoContentAnalyzer.Core.Interfaces;
using VideoContentAnalyzer.Core.Models;
using VideoContentAnalyzer.Core.Services;
using VideoContentAnalyzer.Infrastructure.AI;
using VideoContentAnalyzer.Infrastructure.Media;
using VideoContentAnalyzer.Infrastructure.Places;
using VideoContentAnalyzer.Infrastructure.Services;
using VideoContentAnalyzer.Infrastructure.Subtitle;
using VideoContentAnalyzer.Infrastructure.YouTube;

var videoArgument = new Argument<string?>("video", "Path to the video file to analyze, or YouTube URL/ID") { Arity = ArgumentArity.ZeroOrOne };
var youtubeOption = new Option<string?>(["--youtube", "-y"], "YouTube URL or video ID to download and analyze");
var subtitleOption = new Option<string?>(["--subtitle", "-s"], "Path to the subtitle file (optional)");
var outputOption = new Option<string>(["--output", "-o"], () => "json", "Output format (json, markdown, text)");
var intervalOption = new Option<int>(["--interval", "-i"], () => 5, "Frame extraction interval in seconds");
var maxFramesOption = new Option<int>(["--max-frames", "-m"], () => 50, "Maximum number of frames to analyze");
var noSubtitlesOption = new Option<bool>(["--no-subtitles"], "Disable subtitle generation/processing");
var verboseOption = new Option<bool>(["--verbose", "-v"], "Enable verbose logging");
var testApiOption = new Option<bool>(["--test-api"], "Test LM Studio API connection");
var keepDownloadOption = new Option<bool>(["--keep-download"], "Keep downloaded video file after analysis");

var rootCommand = new RootCommand("Video Content Analyzer - 使用 AI 分析影片內容")
{
    videoArgument,
    youtubeOption,
    subtitleOption,
    outputOption,
    intervalOption,
    maxFramesOption,
    noSubtitlesOption,
    verboseOption,
    testApiOption,
    keepDownloadOption
};

rootCommand.SetHandler(async (InvocationContext context) =>
{
    try
    {
        // Get values from context
        var videoPath = context.ParseResult.GetValueForArgument(videoArgument);
        var youtubeUrl = context.ParseResult.GetValueForOption(youtubeOption);
        var subtitlePath = context.ParseResult.GetValueForOption(subtitleOption);
        var outputFormat = context.ParseResult.GetValueForOption(outputOption);
        var interval = context.ParseResult.GetValueForOption(intervalOption);
        var maxFrames = context.ParseResult.GetValueForOption(maxFramesOption);
        var noSubtitles = context.ParseResult.GetValueForOption(noSubtitlesOption);
        var verbose = context.ParseResult.GetValueForOption(verboseOption);
        var testApi = context.ParseResult.GetValueForOption(testApiOption);
        var keepDownload = context.ParseResult.GetValueForOption(keepDownloadOption);

        // Setup host and services
        var host = CreateHostBuilder(verbose).Build();
        
        using var scope = host.Services.CreateScope();
        var logger = scope.ServiceProvider.GetRequiredService<ILogger<Program>>();

        // Display startup banner
        AnsiConsole.Write(
            new FigletText("Video Analyzer")
                .LeftJustified()
                .Color(Color.Blue));

        AnsiConsole.MarkupLine("[bold]影片內容分析工具[/]");

        // API 測試模式
        if (testApi)
        {
            var lmStudioOptions = scope.ServiceProvider.GetRequiredService<IOptions<LMStudioOptions>>().Value;
            var testResult = await LMStudioTester.TestLMStudioConnectionAsync(lmStudioOptions);
            Environment.Exit(testResult ? 0 : 1);
            return;
        }

        // 處理 YouTube 下載
        string? finalVideoPath = videoPath;
        string? downloadedSubtitlePath = null;
        bool isYouTubeDownload = false;
        YouTubeVideoInfo? youtubeVideoInfo = null;
        
        if (!string.IsNullOrEmpty(youtubeUrl))
        {
            var youtubeService = scope.ServiceProvider.GetRequiredService<IYouTubeDownloadService>();
            
            AnsiConsole.MarkupLine($"[green]正在下載 YouTube 影片：[/]{youtubeUrl}");
            
            var downloadResult = await youtubeService.DownloadVideoAsync(youtubeUrl);
            if (downloadResult.Success)
            {
                finalVideoPath = downloadResult.VideoPath;
                downloadedSubtitlePath = downloadResult.PreferredSubtitlePath;
                isYouTubeDownload = true;
                youtubeVideoInfo = downloadResult.VideoInfo;
                
                AnsiConsole.MarkupLine($"[green]✓ 下載成功：[/]{downloadResult.VideoInfo?.Title}");
                AnsiConsole.MarkupLine($"[blue]檔案位置：[/]{finalVideoPath}");
                AnsiConsole.MarkupLine($"[blue]檔案大小：[/]{downloadResult.FileSizeBytes / 1024 / 1024:F2} MB");
                AnsiConsole.MarkupLine($"[blue]下載耗時：[/]{downloadResult.DownloadDuration.TotalSeconds:F1} 秒");
                
                if (downloadResult.SubtitlePaths.Any())
                {
                    AnsiConsole.MarkupLine($"[blue]字幕檔案：[/]{downloadResult.SubtitlePaths.Count} 個");
                    if (!string.IsNullOrEmpty(downloadedSubtitlePath))
                    {
                        AnsiConsole.MarkupLine($"[blue]使用字幕：[/]{Path.GetFileName(downloadedSubtitlePath)}");
                    }
                }
            }
            else
            {
                AnsiConsole.MarkupLine($"[red]✗ YouTube 影片下載失敗：{downloadResult.ErrorMessage?.Replace("[", "[[").Replace("]", "]]")}[/]");
                Environment.Exit(1);
            }
        }
        else if (string.IsNullOrEmpty(finalVideoPath))
        {
            // 檢查第一個參數是否為 YouTube URL
            if (!string.IsNullOrEmpty(videoPath) && await host.Services.GetRequiredService<IYouTubeDownloadService>().IsValidYouTubeUrlAsync(videoPath))
            {
                var youtubeService = scope.ServiceProvider.GetRequiredService<IYouTubeDownloadService>();
                AnsiConsole.MarkupLine($"[green]偵測到 YouTube URL，正在下載：[/]{videoPath}");
                
                var downloadResult = await youtubeService.DownloadVideoAsync(videoPath);
                if (downloadResult.Success)
                {
                    finalVideoPath = downloadResult.VideoPath;
                    downloadedSubtitlePath = downloadResult.PreferredSubtitlePath;
                    isYouTubeDownload = true;
                    youtubeVideoInfo = downloadResult.VideoInfo;
                    
                    AnsiConsole.MarkupLine($"[green]✓ 下載成功：[/]{downloadResult.VideoInfo?.Title}");
                    AnsiConsole.MarkupLine($"[blue]檔案位置：[/]{finalVideoPath}");
                    
                    if (downloadResult.SubtitlePaths.Any())
                    {
                        AnsiConsole.MarkupLine($"[blue]字幕檔案：[/]{downloadResult.SubtitlePaths.Count} 個");
                        if (!string.IsNullOrEmpty(downloadedSubtitlePath))
                        {
                            AnsiConsole.MarkupLine($"[blue]使用字幕：[/]{Path.GetFileName(downloadedSubtitlePath)}");
                        }
                    }
                }
                else
                {
                    AnsiConsole.MarkupLine($"[red]✗ YouTube 影片下載失敗：{downloadResult.ErrorMessage?.Replace("[", "[[").Replace("]", "]]")}[/]");
                    Environment.Exit(1);
                }
            }
            else
            {
                AnsiConsole.MarkupLine("[red]錯誤：請提供影片檔案路徑、YouTube URL，或使用 --test-api 測試 API 連接[/]");
                AnsiConsole.MarkupLine("[yellow]用法: dotnet run video.mp4[/]");
                AnsiConsole.MarkupLine("[yellow]或者: dotnet run --youtube https://www.youtube.com/watch?v=VIDEO_ID[/]");
                AnsiConsole.MarkupLine("[yellow]或者: dotnet run --test-api[/]");
                Environment.Exit(1);
            }
        }

        var analyzer = scope.ServiceProvider.GetRequiredService<IVideoAnalyzer>();
        
        AnsiConsole.MarkupLine($"[green]影片路径：[/]{finalVideoPath}");

        // Validate input
        if (!File.Exists(finalVideoPath))
        {
            AnsiConsole.MarkupLine($"[red]錯誤：找不到影片文件 {finalVideoPath}[/]");
            Environment.Exit(1);
        }

        // 決定最終使用的字幕路徑：優先使用下載的字幕，其次是命令行提供的字幕
        string? finalSubtitlePath = null;
        
        if (!string.IsNullOrEmpty(downloadedSubtitlePath) && File.Exists(downloadedSubtitlePath))
        {
            finalSubtitlePath = downloadedSubtitlePath;
            AnsiConsole.MarkupLine($"[green]將使用下載的字幕：[/]{Path.GetFileName(finalSubtitlePath)}");
        }
        else if (!string.IsNullOrEmpty(subtitlePath))
        {
            if (File.Exists(subtitlePath))
            {
                finalSubtitlePath = subtitlePath;
                AnsiConsole.MarkupLine($"[green]將使用提供的字幕：[/]{Path.GetFileName(finalSubtitlePath)}");
            }
            else
            {
                AnsiConsole.MarkupLine($"[yellow]警告：找不到字幕文件 {subtitlePath}，將嘗試自動生成字幕[/]");
            }
        }

        // Create analysis request
        var request = new VideoAnalysisRequest
        {
            VideoPath = finalVideoPath,
            SubtitlePath = finalSubtitlePath,
            YouTubeMetadata = youtubeVideoInfo,
            Options = new VideoAnalysisOptions
            {
                FrameExtractionIntervalSeconds = interval,
                MaxFramesPerVideo = maxFrames,
                GenerateSubtitlesIfMissing = !noSubtitles,
                EnableTextRecognition = true,
                OutputFormat = outputFormat
            }
        };

        // Run analysis with progress
        VideoAnalysisResult? result = null;
        
        await AnsiConsole.Progress()
            .StartAsync(async ctx =>
            {
                var task = ctx.AddTask("[green]分析影片內容[/]");
                task.MaxValue = 100;

                var progress = new Progress<AnalysisProgress>(p =>
                {
                    task.Value = p.ProgressPercentage;
                    task.Description = $"[green]{p.CurrentStage}[/] - {p.Message}";
                });

                result = await analyzer.AnalyzeVideoAsync(request, progress);
            });

        if (result == null)
        {
            AnsiConsole.MarkupLine("[red]分析失敗[/]");
            Environment.Exit(1);
        }

        // Display results
        DisplayResults(result, outputFormat);

        // Save results to file
        await SaveResultsAsync(result, outputFormat, finalVideoPath);

        // Clean up downloaded file if requested
        if (isYouTubeDownload && !keepDownload)
        {
            try
            {
                File.Delete(finalVideoPath);
                AnsiConsole.MarkupLine("[yellow]✓ 已清理下載的影片檔案[/]");
            }
            catch (Exception ex)
            {
                AnsiConsole.MarkupLine($"[yellow]⚠ 清理下載檔案時發生錯誤: {ex.Message}[/]");
            }
        }

    }
    catch (Exception ex)
    {
        AnsiConsole.WriteException(ex);
        Environment.Exit(1);
    }

});

return await rootCommand.InvokeAsync(args);

static IHostBuilder CreateHostBuilder(bool verbose) =>
    new HostBuilder()
        .ConfigureAppConfiguration((context, config) =>
        {
            // 清除所有預設設定
            config.Sources.Clear();
            
            // 按照明確順序添加配置源
            config.AddYamlFile("appsettings.yaml", optional: true, reloadOnChange: true);
            config.AddEnvironmentVariables();
            
            // 最後，特別處理 YOUTUBE_API_KEY 環境變數，確保它覆蓋所有其他設定
            var youtubeApiKey = Environment.GetEnvironmentVariable("YOUTUBE_API_KEY");
            if (!string.IsNullOrEmpty(youtubeApiKey))
            {
                config.AddInMemoryCollection(new Dictionary<string, string?>
                {
                    ["YouTubeApi:ApiKey"] = youtubeApiKey
                });
            }
        })
        .UseSerilog((context, configuration) =>
        {
            var logLevel = verbose ? Serilog.Events.LogEventLevel.Debug : Serilog.Events.LogEventLevel.Information;

            configuration
                .MinimumLevel.Is(logLevel)
                .WriteTo.Console()
                .WriteTo.File("./data/logs/video-analyzer-.log",
                    rollingInterval: RollingInterval.Day,
                    retainedFileCountLimit: 7,
                    outputTemplate: "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] {SourceContext}: {Message:lj}{NewLine}{Exception}");
        })
        .ConfigureServices((context, services) =>
        {
            // Configuration
            services.Configure<LMStudioOptions>(context.Configuration.GetSection("LMStudio"));
            services.Configure<WhisperOptions>(context.Configuration.GetSection("Whisper"));
            services.Configure<FrameExtractionOptions>(context.Configuration.GetSection("FrameExtraction"));
            services.Configure<AdvancedOptions>(context.Configuration.GetSection("Advanced"));
            services.Configure<PlaceRecognitionOptions>(context.Configuration.GetSection("PlaceRecognition"));
            services.Configure<YouTubeDownloadOptions>(context.Configuration.GetSection("YouTubeDownload"));
            services.Configure<YouTubeApiOptions>(context.Configuration.GetSection("YouTubeApi"));
            services.Configure<GooglePlacesOptions>(context.Configuration.GetSection("GooglePlaces"));

            // Core services
            services.AddScoped<IVideoAnalyzer, VideoAnalyzer>();
            
            // Infrastructure services
            services.AddScoped<IVideoFrameExtractor, VideoFrameExtractor>();
            services.AddScoped<IAIAnalysisService, AIAnalysisService>();
            services.AddScoped<ILMStudioClient, LMStudioClient>();
            services.AddScoped<ISubtitleService, SubtitleService>();
            services.AddScoped<IYouTubeMetadataFormatter, YouTubeMetadataFormatter>();
            services.AddScoped<IWhisperService, WhisperService>();
            services.AddScoped<IYouTubeDownloadService, YouTubeDownloadService>();
            services.AddScoped<IYouTubeApiService, YouTubeApiService>();
            services.AddScoped<IPlaceDetectionService, GooglePlacesDetectionService>();

            services.AddHttpClient();
        });

static void DisplayResults(VideoAnalysisResult result, string outputFormat)
{
    AnsiConsole.MarkupLine("\n[bold underline]分析結果[/]\n");
    
    var table = new Table();
    table.AddColumn("項目");
    table.AddColumn("資訊");

    table.AddRow("影片時長", result.VideoDuration.ToString(@"hh\:mm\:ss"));
    table.AddRow("分析時間", result.AnalysisTimestamp.ToString("yyyy-MM-dd HH:mm:ss"));
    table.AddRow("分析幀數", result.FrameAnalyses.Count.ToString());
    table.AddRow("字幕段數", result.SubtitleSegments.Count.ToString());

    AnsiConsole.Write(table);

    // Performance metrics
    if (result.PerformanceMetrics.TotalFramesAnalyzed > 0)
    {
        AnsiConsole.MarkupLine("\n[bold underline]效能統計[/]\n");
        
        var perfTable = new Table();
        perfTable.AddColumn("統計項目");
        perfTable.AddColumn("數值");

        perfTable.AddRow("總分析時間", result.PerformanceMetrics.TotalAnalysisTime.ToString(@"hh\:mm\:ss\.fff"));
        perfTable.AddRow("平均單幀分析時間", $"{result.PerformanceMetrics.AverageFrameAnalysisTime.TotalMilliseconds:F0} ms");
        perfTable.AddRow("最快單幀分析時間", $"{result.PerformanceMetrics.FastestFrameAnalysisTime.TotalMilliseconds:F0} ms");
        perfTable.AddRow("最慢單幀分析時間", $"{result.PerformanceMetrics.SlowestFrameAnalysisTime.TotalMilliseconds:F0} ms");
        perfTable.AddRow("成功分析幀數", result.PerformanceMetrics.SuccessfulAnalyses.ToString());
        perfTable.AddRow("失敗分析幀數", result.PerformanceMetrics.FailedAnalyses.ToString());
        
        var successRate = result.PerformanceMetrics.TotalFramesAnalyzed > 0 
            ? (result.PerformanceMetrics.SuccessfulAnalyses * 100.0 / result.PerformanceMetrics.TotalFramesAnalyzed) 
            : 0;
        perfTable.AddRow("成功率", $"{successRate:F1}%");

        AnsiConsole.Write(perfTable);
    }

    // 截圖分析結果
    AnsiConsole.MarkupLine("\n[bold underline green]🖼️ 截圖分析結果[/]\n");
    
    if (!string.IsNullOrEmpty(result.Summary.FrameAnalysisResult.Summary))
    {
        AnsiConsole.MarkupLine("[bold]摘要：[/]");
        AnsiConsole.MarkupLine($"[italic]{result.Summary.FrameAnalysisResult.Summary}[/]");
        AnsiConsole.WriteLine();
    }

    if (!string.IsNullOrEmpty(result.Summary.FrameAnalysisResult.DetailedDescription))
    {
        AnsiConsole.MarkupLine("[bold]詳細描述：[/]");
        AnsiConsole.MarkupLine($"[dim]{result.Summary.FrameAnalysisResult.DetailedDescription}[/]");
        AnsiConsole.WriteLine();
    }

    // 截圖中檢測到的場所資訊
    if (result.Summary.FrameAnalysisResult.PlaceInfos.Any())
    {
        AnsiConsole.MarkupLine("[bold]識別到的餐廳/景點：[/]");
        foreach (var place in result.Summary.FrameAnalysisResult.PlaceInfos.Take(10))
        {
            // 基本資訊
            var placeInfo = $"• [bold]{place.Name ?? "未知名稱"}[/]";
            AnsiConsole.MarkupLine(placeInfo);
            
            // 詳細資訊縮排顯示
            if (!string.IsNullOrEmpty(place.Category))
                AnsiConsole.MarkupLine($"  [dim]類型：[/] {place.Category}");
            if (!string.IsNullOrEmpty(place.Address))
                AnsiConsole.MarkupLine($"  [dim]地址：[/] {place.Address}");
            if (!string.IsNullOrEmpty(place.Phone))
                AnsiConsole.MarkupLine($"  [dim]電話：[/] {place.Phone}");
            if (!string.IsNullOrEmpty(place.BusinessHours))
                AnsiConsole.MarkupLine($"  [dim]營業時間：[/] {place.BusinessHours}");
            if (!string.IsNullOrEmpty(place.Website))
                AnsiConsole.MarkupLine($"  [dim]網站：[/] [link]{place.Website}[/]");
                
            // Google Places 專屬資訊
            if (place.GoogleRating.HasValue)
            {
                var stars = new string('⭐', (int)Math.Round(place.GoogleRating.Value));
                AnsiConsole.MarkupLine($"  [dim]Google 評分：[/] [yellow]{place.GoogleRating:F1}[/] {stars}");
            }
            
            if (place.GoogleUserRatingsTotal.HasValue && place.GoogleUserRatingsTotal > 0)
                AnsiConsole.MarkupLine($"  [dim]評論數：[/] {place.GoogleUserRatingsTotal} 則評論");
            
            if (place.GooglePriceLevel.HasValue)
            {
                var priceLevel = place.GooglePriceLevel.Value switch
                {
                    0 => "免費",
                    1 => "便宜 ($)",
                    2 => "中等 ($$)",
                    3 => "昂貴 ($$$)",
                    4 => "非常昂貴 ($$$$)",
                    _ => "未知"
                };
                AnsiConsole.MarkupLine($"  [dim]價格等級：[/] [green]{priceLevel}[/]");
            }
            
            if (!string.IsNullOrWhiteSpace(place.GooglePlaceId))
                AnsiConsole.MarkupLine($"  [dim]Google Maps：[/] [link]https://www.google.com/maps/place/?q=place_id:{place.GooglePlaceId}[/]");
            
            // 信心度
            if (place.Confidence > 0)
            {
                var confidenceColor = place.Confidence >= 0.8 ? "green" : place.Confidence >= 0.6 ? "yellow" : "red";
                AnsiConsole.MarkupLine($"  [dim]信心度：[/] [{confidenceColor}]{place.Confidence:F2}[/]");
            }
                
            AnsiConsole.WriteLine();
        }
        AnsiConsole.WriteLine();
    }

    // 截圖中的主要活動
    if (result.Summary.FrameAnalysisResult.MainActivities.Any())
    {
        AnsiConsole.MarkupLine("[bold]主要活動：[/]");
        foreach (var activity in result.Summary.FrameAnalysisResult.MainActivities.Take(5))
        {
            AnsiConsole.MarkupLine($"• {activity}");
        }
        AnsiConsole.WriteLine();
    }

    // 截圖中檢測到的物品
    if (result.Summary.FrameAnalysisResult.DetectedObjects.Any())
    {
        AnsiConsole.MarkupLine("[bold]檢測到的物品：[/]");
        AnsiConsole.MarkupLine(string.Join(", ", result.Summary.FrameAnalysisResult.DetectedObjects.Take(10)));
        AnsiConsole.WriteLine();
    }

    // 截圖中檢測到的文字
    if (result.Summary.FrameAnalysisResult.DetectedText.Any())
    {
        AnsiConsole.MarkupLine("[bold]檢測到的文字：[/]");
        AnsiConsole.MarkupLine(string.Join(", ", result.Summary.FrameAnalysisResult.DetectedText.Take(10)));
        AnsiConsole.WriteLine();
    }

    // 字幕分析結果
    AnsiConsole.MarkupLine("\n[bold underline blue]🎵 字幕分析結果[/]\n");
    
    if (!string.IsNullOrEmpty(result.Summary.SubtitleAnalysisResult.Summary))
    {
        AnsiConsole.MarkupLine("[bold]摘要：[/]");
        AnsiConsole.MarkupLine($"[italic]{result.Summary.SubtitleAnalysisResult.Summary}[/]");
        AnsiConsole.WriteLine();
    }

    if (!string.IsNullOrEmpty(result.Summary.SubtitleAnalysisResult.DetailedDescription))
    {
        AnsiConsole.MarkupLine("[bold]詳細描述：[/]");
        AnsiConsole.MarkupLine($"[dim]{result.Summary.SubtitleAnalysisResult.DetailedDescription}[/]");
        AnsiConsole.WriteLine();
    }

    // 字幕中的主要話題
    if (result.Summary.SubtitleAnalysisResult.MainTopics.Any())
    {
        AnsiConsole.MarkupLine("[bold]主要話題：[/]");
        foreach (var topic in result.Summary.SubtitleAnalysisResult.MainTopics.Take(5))
        {
            AnsiConsole.MarkupLine($"• {topic}");
        }
        AnsiConsole.WriteLine();
    }

    // 字幕中的關鍵要點
    if (result.Summary.SubtitleAnalysisResult.KeyPoints.Any())
    {
        AnsiConsole.MarkupLine("[bold]關鍵要點：[/]");
        AnsiConsole.MarkupLine(string.Join(", ", result.Summary.SubtitleAnalysisResult.KeyPoints.Take(10)));
        AnsiConsole.WriteLine();
    }

    // 字幕中提及的地點
    if (result.Summary.SubtitleAnalysisResult.MentionedPlaces.Any())
    {
        AnsiConsole.MarkupLine("[bold]提及的地點：[/]");
        AnsiConsole.MarkupLine(string.Join(", ", result.Summary.SubtitleAnalysisResult.MentionedPlaces.Take(10)));
        AnsiConsole.WriteLine();
    }

    // 重要引語
    if (result.Summary.SubtitleAnalysisResult.ImportantQuotes.Any())
    {
        AnsiConsole.MarkupLine("[bold]重要引語：[/]");
        foreach (var quote in result.Summary.SubtitleAnalysisResult.ImportantQuotes.Take(3))
        {
            AnsiConsole.MarkupLine($"[dim]\"{quote}\"[/]");
        }
        AnsiConsole.WriteLine();
    }

    // 內容結構
    if (!string.IsNullOrEmpty(result.Summary.SubtitleAnalysisResult.ContentStructure))
    {
        AnsiConsole.MarkupLine("[bold]內容結構：[/]");
        AnsiConsole.MarkupLine($"[dim]{result.Summary.SubtitleAnalysisResult.ContentStructure}[/]");
        AnsiConsole.WriteLine();
    }

    // 總體摘要（融合版本）
    AnsiConsole.MarkupLine("\n[bold underline yellow]📋 總體摘要[/]\n");
    if (!string.IsNullOrEmpty(result.Summary.OverallDescription))
    {
        AnsiConsole.MarkupLine("[bold]影片總結：[/]");
        AnsiConsole.MarkupLine($"[italic]{result.Summary.OverallDescription}[/]");
    }
}

static async Task SaveResultsAsync(VideoAnalysisResult result, string outputFormat, string videoPath)
{
    var outputFileName = $"{Path.GetFileNameWithoutExtension(videoPath)}_analysis";
    
    // 確保 data/results 目錄存在
    var resultsDirectory = "./data/results";
    if (!Directory.Exists(resultsDirectory))
    {
        Directory.CreateDirectory(resultsDirectory);
    }
    
    var outputPath = outputFormat.ToLower() switch
    {
        "json" => Path.Combine(resultsDirectory, $"{outputFileName}.json"),
        "markdown" or "md" => Path.Combine(resultsDirectory, $"{outputFileName}.md"),
        "text" or "txt" => Path.Combine(resultsDirectory, $"{outputFileName}.txt"),
        _ => Path.Combine(resultsDirectory, $"{outputFileName}.json")
    };

    var content = outputFormat.ToLower() switch
    {
        "json" => JsonSerializer.Serialize(result, new JsonSerializerOptions 
        { 
            WriteIndented = true,
            Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
        }),
        "markdown" or "md" => GenerateMarkdownReport(result),
        "text" or "txt" => GenerateTextReport(result),
        _ => JsonSerializer.Serialize(result, new JsonSerializerOptions { WriteIndented = true })
    };

    await File.WriteAllTextAsync(outputPath, content, System.Text.Encoding.UTF8);
    AnsiConsole.MarkupLine($"\n[green]分析結果已保存到：[/]{outputPath}");
}

static string GenerateMarkdownReport(VideoAnalysisResult result)
{
    var md = new System.Text.StringBuilder();
    
    md.AppendLine($"# 影片內容分析報告");
    md.AppendLine();
    md.AppendLine($"**影片路徑：** {result.VideoPath}");
    md.AppendLine($"**影片時長：** {result.VideoDuration:hh\\:mm\\:ss}");
    md.AppendLine($"**分析時間：** {result.AnalysisTimestamp:yyyy-MM-dd HH:mm:ss}");
    md.AppendLine();

    // 截圖分析結果
    md.AppendLine("## 🖼️ 截圖分析結果");
    md.AppendLine();

    if (!string.IsNullOrEmpty(result.Summary.FrameAnalysisResult.Summary))
    {
        md.AppendLine("### 摘要");
        md.AppendLine(result.Summary.FrameAnalysisResult.Summary);
        md.AppendLine();
    }

    if (!string.IsNullOrEmpty(result.Summary.FrameAnalysisResult.DetailedDescription))
    {
        md.AppendLine("### 詳細描述");
        md.AppendLine(result.Summary.FrameAnalysisResult.DetailedDescription);
        md.AppendLine();
    }

    // 截圖中識別到的場所資訊
    if (result.Summary.FrameAnalysisResult.PlaceInfos.Any())
    {
        md.AppendLine("### 識別到的餐廳/景點");
        foreach (var place in result.Summary.FrameAnalysisResult.PlaceInfos)
        {
            md.AppendLine($"#### {place.Name ?? "未知名稱"}");
            
            // 基本資訊
            if (!string.IsNullOrEmpty(place.Category))
                md.AppendLine($"- **類型：** {place.Category}");
            if (!string.IsNullOrEmpty(place.Address))
                md.AppendLine($"- **地址：** {place.Address}");
            if (!string.IsNullOrEmpty(place.Phone))
                md.AppendLine($"- **電話：** {place.Phone}");
            if (!string.IsNullOrEmpty(place.BusinessHours))
                md.AppendLine($"- **營業時間：** {place.BusinessHours}");
            if (!string.IsNullOrEmpty(place.Website))
                md.AppendLine($"- **網站：** [{place.Website}]({place.Website})");
            if (!string.IsNullOrEmpty(place.Description))
                md.AppendLine($"- **描述：** {place.Description}");
                
            // Google Places 特有資訊
            if (place.GoogleRating.HasValue)
            {
                var stars = new string('⭐', (int)Math.Round(place.GoogleRating.Value));
                md.AppendLine($"- **Google 評分：** {place.GoogleRating:F1}/5.0 {stars}");
            }
            
            if (place.GoogleUserRatingsTotal.HasValue && place.GoogleUserRatingsTotal > 0)
                md.AppendLine($"- **評論數量：** {place.GoogleUserRatingsTotal} 則評論");
            
            if (place.GooglePriceLevel.HasValue)
            {
                var priceLevel = place.GooglePriceLevel.Value switch
                {
                    0 => "免費",
                    1 => "便宜 ($)",
                    2 => "中等 ($$)",
                    3 => "昂貴 ($$$)",
                    4 => "非常昂貴 ($$$$)",
                    _ => "未知"
                };
                md.AppendLine($"- **價格等級：** {priceLevel}");
            }
            
            if (!string.IsNullOrWhiteSpace(place.GooglePlaceId))
                md.AppendLine($"- **Google Maps：** [查看地圖](https://www.google.com/maps/place/?q=place_id:{place.GooglePlaceId})");
                
            // 信心度
            if (place.Confidence > 0)
                md.AppendLine($"- **信心度：** {place.Confidence:F2}");
                
            md.AppendLine();
        }
    }

    // 截圖中的主要活動
    if (result.Summary.FrameAnalysisResult.MainActivities.Any())
    {
        md.AppendLine("### 主要活動");
        foreach (var activity in result.Summary.FrameAnalysisResult.MainActivities)
        {
            md.AppendLine($"- {activity}");
        }
        md.AppendLine();
    }

    // 截圖中檢測到的物品
    if (result.Summary.FrameAnalysisResult.DetectedObjects.Any())
    {
        md.AppendLine("### 檢測到的物品");
        md.AppendLine(string.Join(", ", result.Summary.FrameAnalysisResult.DetectedObjects));
        md.AppendLine();
    }

    // 截圖中檢測到的文字
    if (result.Summary.FrameAnalysisResult.DetectedText.Any())
    {
        md.AppendLine("### 檢測到的文字");
        md.AppendLine(string.Join(", ", result.Summary.FrameAnalysisResult.DetectedText));
        md.AppendLine();
    }

    // 場景變化
    if (result.Summary.FrameAnalysisResult.SceneChanges.Any())
    {
        md.AppendLine("### 場景變化");
        foreach (var change in result.Summary.FrameAnalysisResult.SceneChanges)
        {
            md.AppendLine($"- **{change.Timestamp:mm\\:ss}** - {change.Description}");
        }
        md.AppendLine();
    }

    // 字幕分析結果
    md.AppendLine("## 🎵 字幕分析結果");
    md.AppendLine();

    if (!string.IsNullOrEmpty(result.Summary.SubtitleAnalysisResult.Summary))
    {
        md.AppendLine("### 摘要");
        md.AppendLine(result.Summary.SubtitleAnalysisResult.Summary);
        md.AppendLine();
    }

    if (!string.IsNullOrEmpty(result.Summary.SubtitleAnalysisResult.DetailedDescription))
    {
        md.AppendLine("### 詳細描述");
        md.AppendLine(result.Summary.SubtitleAnalysisResult.DetailedDescription);
        md.AppendLine();
    }

    // 字幕中的主要話題
    if (result.Summary.SubtitleAnalysisResult.MainTopics.Any())
    {
        md.AppendLine("### 主要話題");
        foreach (var topic in result.Summary.SubtitleAnalysisResult.MainTopics)
        {
            md.AppendLine($"- {topic}");
        }
        md.AppendLine();
    }

    // 字幕中的關鍵要點
    if (result.Summary.SubtitleAnalysisResult.KeyPoints.Any())
    {
        md.AppendLine("### 關鍵要點");
        md.AppendLine(string.Join(", ", result.Summary.SubtitleAnalysisResult.KeyPoints));
        md.AppendLine();
    }

    // 字幕中提及的地點
    if (result.Summary.SubtitleAnalysisResult.MentionedPlaces.Any())
    {
        md.AppendLine("### 提及的地點");
        md.AppendLine(string.Join(", ", result.Summary.SubtitleAnalysisResult.MentionedPlaces));
        md.AppendLine();
    }

    // 重要引語
    if (result.Summary.SubtitleAnalysisResult.ImportantQuotes.Any())
    {
        md.AppendLine("### 重要引語");
        foreach (var quote in result.Summary.SubtitleAnalysisResult.ImportantQuotes)
        {
            md.AppendLine($"- {quote}");
        }
        md.AppendLine();
    }

    // 內容結構
    if (!string.IsNullOrEmpty(result.Summary.SubtitleAnalysisResult.ContentStructure))
    {
        md.AppendLine("### 內容結構");
        md.AppendLine(result.Summary.SubtitleAnalysisResult.ContentStructure);
        md.AppendLine();
    }

    // 總體摘要（融合版本）
    md.AppendLine("## 📋 總體摘要");
    md.AppendLine();

    if (!string.IsNullOrEmpty(result.Summary.OverallDescription))
    {
        md.AppendLine("### 影片總結");
        md.AppendLine(result.Summary.OverallDescription);
        md.AppendLine();
    }

    // 關鍵幀分析
    if (result.FrameAnalyses.Any())
    {
        md.AppendLine("## 📸 關鍵幀詳細分析");
        foreach (var frame in result.FrameAnalyses.Take(10))
        {
            md.AppendLine($"### {frame.Timestamp:mm\\:ss}");
            md.AppendLine(frame.Scene.MainDescription);
            md.AppendLine();
        }
    }

    return md.ToString();
}

static string GenerateTextReport(VideoAnalysisResult result)
{
    var txt = new System.Text.StringBuilder();
    
    txt.AppendLine("影片內容分析報告");
    txt.AppendLine("===================");
    txt.AppendLine();
    txt.AppendLine($"影片路徑：{result.VideoPath}");
    txt.AppendLine($"影片時長：{result.VideoDuration:hh\\:mm\\:ss}");
    txt.AppendLine($"分析時間：{result.AnalysisTimestamp:yyyy-MM-dd HH:mm:ss}");
    txt.AppendLine();

    // 截圖分析結果
    txt.AppendLine("🖼️ 截圖分析結果");
    txt.AppendLine("-------------------");
    txt.AppendLine();

    if (!string.IsNullOrEmpty(result.Summary.FrameAnalysisResult.Summary))
    {
        txt.AppendLine("摘要：");
        txt.AppendLine(result.Summary.FrameAnalysisResult.Summary);
        txt.AppendLine();
    }

    if (!string.IsNullOrEmpty(result.Summary.FrameAnalysisResult.DetailedDescription))
    {
        txt.AppendLine("詳細描述：");
        txt.AppendLine(result.Summary.FrameAnalysisResult.DetailedDescription);
        txt.AppendLine();
    }

    // 截圖中識別到的場所資訊
    if (result.Summary.FrameAnalysisResult.PlaceInfos.Any())
    {
        txt.AppendLine("識別到的餐廳/景點：");
        foreach (var place in result.Summary.FrameAnalysisResult.PlaceInfos)
        {
            txt.AppendLine($"• {place.Name ?? "未知名稱"}");
            
            // 基本資訊
            if (!string.IsNullOrEmpty(place.Category))
                txt.AppendLine($"  類型: {place.Category}");
            if (!string.IsNullOrEmpty(place.Address))
                txt.AppendLine($"  地址: {place.Address}");
            if (!string.IsNullOrEmpty(place.Phone))
                txt.AppendLine($"  電話: {place.Phone}");
            if (!string.IsNullOrEmpty(place.BusinessHours))
                txt.AppendLine($"  營業時間: {place.BusinessHours}");
            if (!string.IsNullOrEmpty(place.Website))
                txt.AppendLine($"  網站: {place.Website}");
            if (!string.IsNullOrEmpty(place.Description))
                txt.AppendLine($"  描述: {place.Description}");
                
            // Google Places 特有資訊
            if (place.GoogleRating.HasValue)
            {
                var stars = new string('★', (int)Math.Round(place.GoogleRating.Value));
                txt.AppendLine($"  Google 評分: {place.GoogleRating:F1}/5.0 {stars}");
            }
            
            if (place.GoogleUserRatingsTotal.HasValue && place.GoogleUserRatingsTotal > 0)
                txt.AppendLine($"  評論數量: {place.GoogleUserRatingsTotal} 則評論");
            
            if (place.GooglePriceLevel.HasValue)
            {
                var priceLevel = place.GooglePriceLevel.Value switch
                {
                    0 => "免費",
                    1 => "便宜 ($)",
                    2 => "中等 ($$)",
                    3 => "昂貴 ($$$)",
                    4 => "非常昂貴 ($$$$)",
                    _ => "未知"
                };
                txt.AppendLine($"  價格等級: {priceLevel}");
            }
            
            if (!string.IsNullOrWhiteSpace(place.GooglePlaceId))
                txt.AppendLine($"  Google Maps: https://www.google.com/maps/place/?q=place_id:{place.GooglePlaceId}");
                
            // 信心度
            if (place.Confidence > 0)
                txt.AppendLine($"  信心度: {place.Confidence:F2}");
                
            txt.AppendLine();
        }
    }

    // 截圖中的主要活動
    if (result.Summary.FrameAnalysisResult.MainActivities.Any())
    {
        txt.AppendLine("主要活動：");
        foreach (var activity in result.Summary.FrameAnalysisResult.MainActivities)
        {
            txt.AppendLine($"• {activity}");
        }
        txt.AppendLine();
    }

    // 截圖中檢測到的物品
    if (result.Summary.FrameAnalysisResult.DetectedObjects.Any())
    {
        txt.AppendLine("檢測到的物品：");
        txt.AppendLine(string.Join(", ", result.Summary.FrameAnalysisResult.DetectedObjects));
        txt.AppendLine();
    }

    // 截圖中檢測到的文字
    if (result.Summary.FrameAnalysisResult.DetectedText.Any())
    {
        txt.AppendLine("檢測到的文字：");
        txt.AppendLine(string.Join(", ", result.Summary.FrameAnalysisResult.DetectedText));
        txt.AppendLine();
    }

    // 字幕分析結果
    txt.AppendLine("🎵 字幕分析結果");
    txt.AppendLine("-------------------");
    txt.AppendLine();

    if (!string.IsNullOrEmpty(result.Summary.SubtitleAnalysisResult.Summary))
    {
        txt.AppendLine("摘要：");
        txt.AppendLine(result.Summary.SubtitleAnalysisResult.Summary);
        txt.AppendLine();
    }

    if (!string.IsNullOrEmpty(result.Summary.SubtitleAnalysisResult.DetailedDescription))
    {
        txt.AppendLine("詳細描述：");
        txt.AppendLine(result.Summary.SubtitleAnalysisResult.DetailedDescription);
        txt.AppendLine();
    }

    // 字幕中的主要話題
    if (result.Summary.SubtitleAnalysisResult.MainTopics.Any())
    {
        txt.AppendLine("主要話題：");
        foreach (var topic in result.Summary.SubtitleAnalysisResult.MainTopics)
        {
            txt.AppendLine($"• {topic}");
        }
        txt.AppendLine();
    }

    // 字幕中的關鍵要點
    if (result.Summary.SubtitleAnalysisResult.KeyPoints.Any())
    {
        txt.AppendLine("關鍵要點：");
        txt.AppendLine(string.Join(", ", result.Summary.SubtitleAnalysisResult.KeyPoints));
        txt.AppendLine();
    }

    // 字幕中提及的地點
    if (result.Summary.SubtitleAnalysisResult.MentionedPlaces.Any())
    {
        txt.AppendLine("提及的地點：");
        txt.AppendLine(string.Join(", ", result.Summary.SubtitleAnalysisResult.MentionedPlaces));
        txt.AppendLine();
    }

    // 重要引語
    if (result.Summary.SubtitleAnalysisResult.ImportantQuotes.Any())
    {
        txt.AppendLine("重要引語：");
        foreach (var quote in result.Summary.SubtitleAnalysisResult.ImportantQuotes)
        {
            txt.AppendLine($"• {quote}");
        }
        txt.AppendLine();
    }

    // 內容結構
    if (!string.IsNullOrEmpty(result.Summary.SubtitleAnalysisResult.ContentStructure))
    {
        txt.AppendLine("內容結構：");
        txt.AppendLine(result.Summary.SubtitleAnalysisResult.ContentStructure);
        txt.AppendLine();
    }

    // 總體摘要（融合版本）
    txt.AppendLine("📋 總體摘要");
    txt.AppendLine("-------------------");
    txt.AppendLine();

    if (!string.IsNullOrEmpty(result.Summary.OverallDescription))
    {
        txt.AppendLine("影片總結：");
        txt.AppendLine(result.Summary.OverallDescription);
        txt.AppendLine();
    }

    return txt.ToString();
}