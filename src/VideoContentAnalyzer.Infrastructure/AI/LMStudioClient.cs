using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using RestSharp;
using SixLabors.ImageSharp;
using SixLabors.ImageSharp.Formats.Jpeg;
using SixLabors.ImageSharp.Processing;
using System.Text.Json;
using System.Text.Json.Serialization;
using VideoContentAnalyzer.Core.Interfaces;

namespace VideoContentAnalyzer.Infrastructure.AI;

public class LMStudioClient : ILMStudioClient
{
    private readonly RestClient _client;
    private readonly LMStudioOptions _options;
    private readonly ILogger<LMStudioClient> _logger;

    public LMStudioClient(IOptions<LMStudioOptions> options, ILogger<LMStudioClient> logger)
    {
        _options = options.Value;
        _logger = logger;
        
        var clientOptions = new RestClientOptions(_options.BaseUrl)
        {
            Timeout = TimeSpan.FromSeconds(_options.TimeoutSeconds)
        };
        _client = new RestClient(clientOptions);
    }

    public async Task<(string result, TimeSpan duration)> SendVisionRequestAsync(string imagePath, string prompt, CancellationToken cancellationToken = default)
    {
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();
        try
        {
            _logger.LogInformation("Sending vision request for image: {ImagePath}", imagePath);
            _logger.LogInformation("Vision Prompt: {Prompt}", prompt);

            var base64Image = await ConvertImageToBase64Async(imagePath, cancellationToken);
            
            var request = new RestRequest("/v1/chat/completions", Method.Post);
            request.AddHeader("Content-Type", "application/json");

            var requestBody = new
            {
                model = _options.VisionModel,
                messages = new[]
                {
                    new
                    {
                        role = "user",
                        content = new object[]
                        {
                            new { type = "text", text = prompt },
                            new { 
                                type = "image_url", 
                                image_url = new { url = $"data:image/jpeg;base64,{base64Image}" }
                            }
                        }
                    }
                },
                max_tokens = _options.MaxTokens,
                temperature = _options.Temperature
            };

            request.AddJsonBody(requestBody);

            var response = await _client.ExecuteAsync(request, cancellationToken);
            
            if (!response.IsSuccessful)
            {
                _logger.LogError("LM Studio API request failed: {StatusCode} - {ErrorMessage}", 
                    response.StatusCode, response.ErrorMessage);
                throw new HttpRequestException($"LM Studio API request failed: {response.StatusCode} - {response.ErrorMessage}");
            }

            _logger.LogDebug("Raw LM Studio vision response: {ResponseContent}", response.Content);
            var jsonResponse = JsonSerializer.Deserialize<LMStudioResponse>(response.Content!);
            var result = jsonResponse?.Choices?.FirstOrDefault()?.Message?.Content ?? string.Empty;
            
            stopwatch.Stop();
            var duration = stopwatch.Elapsed;
            
            _logger.LogDebug("Parsed vision result: {Result}", result);
            _logger.LogInformation("Vision request completed successfully in {Duration}ms", duration.TotalMilliseconds);
            return (result, duration);
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(ex, "Error in vision request for image: {ImagePath}, Duration: {Duration}ms", imagePath, stopwatch.Elapsed.TotalMilliseconds);
            throw;
        }
    }

    public async Task<string> SendTextRequestAsync(string prompt, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Sending text request to LM Studio");
            _logger.LogInformation("Text Prompt: {Prompt}", prompt);

            var request = new RestRequest("/v1/chat/completions", Method.Post);
            request.AddHeader("Content-Type", "application/json");

            var requestBody = new
            {
                model = _options.TextModel,
                messages = new[]
                {
                    new { role = "user", content = prompt }
                },
                max_tokens = _options.MaxTokens,
                temperature = _options.Temperature
            };

            request.AddJsonBody(requestBody);

            var response = await _client.ExecuteAsync(request, cancellationToken);
            
            if (!response.IsSuccessful)
            {
                _logger.LogError("LM Studio text API request failed: {StatusCode} - {ErrorMessage}", 
                    response.StatusCode, response.ErrorMessage);
                throw new HttpRequestException($"LM Studio API request failed: {response.StatusCode} - {response.ErrorMessage}");
            }

            _logger.LogDebug("Raw LM Studio text response: {ResponseContent}", response.Content);
            var jsonResponse = JsonSerializer.Deserialize<LMStudioResponse>(response.Content!);
            var result = jsonResponse?.Choices?.FirstOrDefault()?.Message?.Content ?? string.Empty;
            
            _logger.LogDebug("Parsed text result: {Result}", result);
            _logger.LogInformation("Text request completed successfully");
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in text request");
            throw;
        }
    }

    public async Task<bool> IsAvailableAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var request = new RestRequest("/v1/models", Method.Get);
            var response = await _client.ExecuteAsync(request, cancellationToken);
            return response.IsSuccessful;
        }
        catch
        {
            return false;
        }
    }

    private async Task<string> ConvertImageToBase64Async(string imagePath, CancellationToken cancellationToken)
    {
        using var image = await Image.LoadAsync(imagePath, cancellationToken);
        
        // Resize if image is too large
        if (image.Width > _options.MaxImageSize || image.Height > _options.MaxImageSize)
        {
            var scale = Math.Min((double)_options.MaxImageSize / image.Width, (double)_options.MaxImageSize / image.Height);
            var newWidth = (int)(image.Width * scale);
            var newHeight = (int)(image.Height * scale);
            
            image.Mutate(x => x.Resize(newWidth, newHeight));
        }

        using var memoryStream = new MemoryStream();
        await image.SaveAsync(memoryStream, new JpegEncoder { Quality = _options.ImageQuality }, cancellationToken);
        
        return Convert.ToBase64String(memoryStream.ToArray());
    }

    public void Dispose()
    {
        _client?.Dispose();
    }
}

public class LMStudioOptions
{
    public string BaseUrl { get; set; } = string.Empty;
    public string VisionModel { get; set; } = string.Empty;
    public string TextModel { get; set; } = string.Empty;
    public int MaxTokens { get; set; } = 1000;
    public double Temperature { get; set; } = 0.7;
    public int TimeoutSeconds { get; set; } = 300;
    public int MaxImageSize { get; set; } = 1024;
    public int ImageQuality { get; set; } = 85;
}

public class PlaceRecognitionOptions
{
    public bool EnablePlaceInfoExtraction { get; set; } = true;
    public List<string> TargetLanguages { get; set; } = ["zh-TW", "ja", "ko", "zh-CN"];
    public double PlaceInfoConfidenceThreshold { get; set; } = 0.3;
    public double TextConfidenceThreshold { get; set; } = 0.5;
    public bool PrioritizePlaceInfoInSummary { get; set; } = true;
    public List<string> PreferredCategories { get; set; } = ["restaurant", "cafe", "attraction", "hotel", "shop"];
}

internal class LMStudioResponse
{
    [JsonPropertyName("choices")]
    public List<LMStudioChoice>? Choices { get; set; }
}

internal class LMStudioChoice
{
    [JsonPropertyName("message")]
    public LMStudioMessage? Message { get; set; }
}

internal class LMStudioMessage
{
    [JsonPropertyName("content")]
    public string? Content { get; set; }
}